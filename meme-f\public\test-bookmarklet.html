<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Drag-to-Save Bookmarklet</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-images {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .image-card {
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            background: white;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .image-card img {
            max-width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 6px;
            cursor: grab;
        }
        .image-card img:active {
            cursor: grabbing;
        }
        .instructions {
            background: #f0f9ff;
            border: 2px solid #0ea5e9;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .warning {
            background: #fef3c7;
            border: 2px solid #f59e0b;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <h1>🧪 Test Drag-to-Save Bookmarklet</h1>

    <div style="background: #10b981; color: white; border-radius: 8px; padding: 20px; margin: 20px 0; text-align: center;">
        <h3>🚀 Quick Start</h3>
        <p>1. <a href="/save-memes.html" target="_blank" style="color: #fef3c7; text-decoration: underline;">Get the bookmarklet</a> → 2. Come back here → 3. Click bookmarklet → 4. Drag memes below!</p>
    </div>

    <div class="instructions">
        <h3>📋 Testing Instructions:</h3>
        <ol>
            <li>Go to <a href="/save-memes.html" target="_blank">save-memes.html</a> and drag the "🎯 Drag to Save Bookmarklet" button to your bookmarks bar</li>
            <li>Come back to this page</li>
            <li>Click your "Drag to Save Bookmarklet" bookmark to activate the floating drop zone</li>
            <li>Try dragging any meme image below onto the floating "🎭 MemeDB" button</li>
            <li>Watch the notifications and see the image get saved to your database!</li>
            <li>Check <a href="/" target="_blank">your main MemeDB page</a> to see the saved memes</li>
        </ol>
    </div>

    <div class="warning">
        <h3>⚠️ Note about External Testing:</h3>
        <p>To test on external websites (Reddit, Twitter, etc.), you'll need to either:</p>
        <ul>
            <li><strong>Deploy to a public domain</strong> (Vercel, Netlify, etc.)</li>
            <li><strong>Use ngrok</strong> to expose localhost publicly</li>
            <li><strong>Test only on HTTP sites</strong> (most modern sites use HTTPS and will block localhost)</li>
        </ul>
    </div>

    <h2>🖼️ Test Images (Try dragging these!)</h2>
    
    <div class="test-images">
        <div class="image-card">
            <img src="https://i.imgflip.com/1bij.jpg" alt="Success Kid Meme" draggable="true">
            <h4>Success Kid</h4>
            <p>Classic meme - drag to test saving!</p>
        </div>

        <div class="image-card">
            <img src="https://i.imgflip.com/30b1gx.jpg" alt="Drake Pointing Meme" draggable="true">
            <h4>Drake Pointing</h4>
            <p>Popular format - perfect for testing</p>
        </div>

        <div class="image-card">
            <img src="https://i.imgflip.com/1g8my4.jpg" alt="Distracted Boyfriend Meme" draggable="true">
            <h4>Distracted Boyfriend</h4>
            <p>Viral meme template</p>
        </div>

        <div class="image-card">
            <img src="https://i.imgflip.com/26am.jpg" alt="Surprised Pikachu" draggable="true">
            <h4>Surprised Pikachu</h4>
            <p>Internet classic - drag to save!</p>
        </div>

        <div class="image-card">
            <img src="https://i.imgflip.com/1ur9b0.jpg" alt="Scroll of Truth" draggable="true">
            <h4>Scroll of Truth</h4>
            <p>Wisdom meme format</p>
        </div>

        <div class="image-card">
            <img src="https://i.imgflip.com/1otk96.jpg" alt="Expanding Brain" draggable="true">
            <h4>Expanding Brain</h4>
            <p>Galaxy brain meme</p>
        </div>

        <div class="image-card">
            <img src="https://i.imgflip.com/1ihzfe.jpg" alt="Sword Cat" draggable="true">
            <h4>Sword Cat</h4>
            <p>Cute and fierce!</p>
        </div>

        <div class="image-card">
            <img src="https://i.imgflip.com/2zo1ki.jpg" alt="Woman Yelling at Cat" draggable="true">
            <h4>Woman Yelling at Cat</h4>
            <p>Table cat meme</p>
        </div>

        <div class="image-card">
            <img src="https://i.imgflip.com/1c1uej.jpg" alt="Leonardo DiCaprio Cheers" draggable="true">
            <h4>Leo Cheers</h4>
            <p>Celebration meme</p>
        </div>
    </div>

    <div class="instructions">
        <h3>🚀 For Production Use:</h3>
        <p>When you deploy your meme database to a real domain, update the bookmarklet URLs to point to your production servers:</p>
        <ul>
            <li><code>API_URL</code>: Your production backend URL</li>
            <li><code>MEME_DB_URL</code>: Your production frontend URL</li>
        </ul>
        <p>Then the bookmarklet will work perfectly on any external website!</p>
    </div>

    <p><a href="/">← Back to Meme Database</a> | <a href="/bookmarklet.html">📌 Get Bookmarklet</a></p>
</body>
</html>
